import { hasTags } from '../../support/utils'

const REGISTER_URL = '/members/register/'
const SEGMENTATION_QUESTIONS = '/welcome/segmentation-questions/'
const PERSONALIZATION_QUESTIONS = '/welcome/personalization-questions/'
const INBOX_PATH = '/views'

if (hasTags('registration', 'enhanced-signup-flow', 'hs-app-ui')) {
  describe('[GROWTH]: Enhanced Signup Flow Experiment', () => {
    it('User Journey: Account Owner Registration and Forwarder Setup (Experimental Variant)', () => {
      // Step 1: User registers for an account
      cy.visitEmailRegistrationPage(REGISTER_URL)

      cy.getCsrfToken().then(csrfToken => {
        // Create user with experimental email (+growthoptin)
        cy.generateExperimentalUser('low', `Test_Enhanced_Flow_Exp`).then(
          user => {
            // Complete the registration form with experimental email
            cy.completeProfile(user)

            // Step 2: User verifies email (mocked via API)
            cy.completeRecaptchaViaAPI(csrfToken)

            // Step 3: User is assigned to the experimental variant via email pattern (+growthoptin)

            // Step 4: User is directed to segmentation questions
            cy.visit(SEGMENTATION_QUESTIONS)
            cy.url().should('include', SEGMENTATION_QUESTIONS)

            // segmentation page shouldn't include the current solution questions when in the experimental variant
            cy.getByCy('DropList.currentSolution').should('not.exist')

            // Step 5: User completes the new segmentation questions
            cy.completeSegmentation({
              company: user.company,
              companyEmployeeCount: '11-50',
              companyIndustry: 'Software',
              shouldDisplayCurrentSolution: false,
            })

            // Step 6: User is directed to the new personalization questions page
            cy.url().should('include', PERSONALIZATION_QUESTIONS)

            // Step 7: User completes the new personalization questions
            cy.completePersonalizationQuestions({
              currentSolution: ['emailInbox', 'helpDesk'],
              signupChannels: ['email', 'liveChat', 'helpCenter'],
            })

            // Step 8: Verify user is redirected to Inbox (inbox is automatically created)
            cy.url().should('include', INBOX_PATH, { timeout: 10000 })
          }
        )
      })
    })

    it('Variant Split Testing - Control', () => {
      // Step 1: User registers for an account
      cy.visitEmailRegistrationPage(REGISTER_URL)

      cy.getCsrfToken().then(csrfToken => {
        // Create user with control email (+growthoptout)
        cy.generateControlUser('low', `Test_Enhanced_Flow_Control`).then(
          user => {
            // Complete the registration form with control email
            cy.completeProfile(user)

            // Step 2: User verifies email (mocked via API)
            cy.completeRecaptchaViaAPI(csrfToken)

            // Step 3: User is assigned to the control variant via email pattern (+growthoptout)

            // Step 4: User completes the original segmentation questions
            cy.visit(SEGMENTATION_QUESTIONS)
            cy.url().should('include', SEGMENTATION_QUESTIONS)
            // segmentation page should include the current solution questions when in the control variant
            cy.getByCy('DropList.currentSolution').should('exist')

            // Complete the segmentation questions
            cy.completeSegmentation({
              company: user.company,
              currentSolution: 'Email',
              companyEmployeeCount: '11-50',
              companyIndustry: 'Software',
            })

            // Step 5: User completes the inbox setup manually
            cy.createNewMailbox(user)

            // Step 6: User is redirected to Inbox
            cy.url().should('include', INBOX_PATH, { timeout: 10000 })

            //  Verify the new welcome modal is not displayed
            cy.getByCy('WelcomeModal').should('not.exist')

            // Verify the blank slate is not displayed
            cy.getByCy('BlankSlate').should('not.exist')
          }
        )
      })
    })
  })
}
